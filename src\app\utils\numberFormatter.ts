// Number formatting utility for statistics display
export const formatNumber = (num: number): string => {
  if (num === 0) return '0';
  
  const absNum = Math.abs(num);
  
  // For numbers less than 1000, show as is
  if (absNum < 1000) {
    return num.toString();
  }
  
  // For thousands (1K - 999K)
  if (absNum < 1000000) {
    const thousands = num / 1000;
    // Show 3 decimal places for numbers less than 10k, 2 for 10k-99k, 1 for 100k+
    if (absNum < 10000) {
      return `${thousands.toFixed(3)}k`;
    } else if (absNum < 100000) {
      return `${thousands.toFixed(2)}k`;
    } else {
      return `${thousands.toFixed(1)}k`;
    }
  }
  
  // For millions (1M - 999M)
  if (absNum < 1000000000) {
    const millions = num / 1000000;
    // Show 3 decimal places for numbers less than 10M, 2 for 10M-99M, 1 for 100M+
    if (absNum < 10000000) {
      return `${millions.toFixed(3)}M`;
    } else if (absNum < 100000000) {
      return `${millions.toFixed(2)}M`;
    } else {
      return `${millions.toFixed(1)}M`;
    }
  }
  
  // For billions (1B+)
  const billions = num / 1000000000;
  if (absNum < 10000000000) {
    return `${billions.toFixed(3)}B`;
  } else if (absNum < 100000000000) {
    return `${billions.toFixed(2)}B`;
  } else {
    return `${billions.toFixed(1)}B`;
  }
};

// Alternative formatter with fixed decimal places
export const formatNumberFixed = (num: number, decimals: number = 3): string => {
  if (num === 0) return '0';
  
  const absNum = Math.abs(num);
  
  // For numbers less than 1000, show as is
  if (absNum < 1000) {
    return num.toString();
  }
  
  // For thousands
  if (absNum < 1000000) {
    const thousands = num / 1000;
    return `${thousands.toFixed(decimals)}k`;
  }
  
  // For millions
  if (absNum < 1000000000) {
    const millions = num / 1000000;
    return `${millions.toFixed(decimals)}M`;
  }
  
  // For billions
  const billions = num / 1000000000;
  return `${billions.toFixed(decimals)}B`;
};

// Formatter specifically for Thai context (using Thai decimal separator if needed)
export const formatNumberThai = (num: number): string => {
  const formatted = formatNumber(num);
  // You can add Thai-specific formatting here if needed
  // For now, keeping the same format as it's internationally recognized
  return formatted;
};
