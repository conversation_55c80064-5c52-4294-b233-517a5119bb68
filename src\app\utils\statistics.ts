// Statistics tracking utility with Firebase integration
import { firebaseService } from '../services/firebaseService';
export interface PageView {
  page: string;
  timestamp: number;
  category?: string;
}

export interface TestSession {
  type: 'mock' | 'category';
  category?: string;
  timestamp: number;
  completed: boolean;
  score?: number;
}

export interface StudySession {
  category: string;
  timestamp: number;
  questionsViewed: number;
}

export interface MockExamResult {
  id?: string;
  userName: string;
  userEmail: string;
  score: number;
  duration: number; // in seconds
  timestamp: number;
}

export interface Statistics {
  totalPageViews: number;
  totalTestTakers: number;
  totalStudyViews: number;
  mockExamTestTakers: number;
  mostPopularPage: string;
  mostPopularCategory: string;
  categoryStats: Array<{
    name: string;
    testTakers: number;
    studyViews: number;
  }>;
  pageViews: Array<{
    page: string;
    views: number;
  }>;
  mockExamLeaderboard: MockExamResult[];
}

// Storage keys (for backward compatibility and session tracking)
const STORAGE_KEYS = {
  PAGE_VIEWS: 'driving_theory_page_views',
  TEST_SESSIONS: 'driving_theory_test_sessions',
  STUDY_SESSIONS: 'driving_theory_study_sessions',
  SESSION_TRACKING: 'driving_theory_session_tracking',
};

// Session tracking to prevent duplicate tracking
const SESSION_ID = Date.now().toString();
const getSessionKey = (type: string, identifier: string) => `${type}_${identifier}_${SESSION_ID}`;

const hasTrackedInSession = (type: string, identifier: string): boolean => {
  try {
    const sessionTracking = getStoredData<string[]>(STORAGE_KEYS.SESSION_TRACKING, []);
    const sessionKey = getSessionKey(type, identifier);
    return sessionTracking.includes(sessionKey);
  } catch {
    return false;
  }
};

const markTrackedInSession = (type: string, identifier: string) => {
  try {
    const sessionTracking = getStoredData<string[]>(STORAGE_KEYS.SESSION_TRACKING, []);
    const sessionKey = getSessionKey(type, identifier);
    if (!sessionTracking.includes(sessionKey)) {
      sessionTracking.push(sessionKey);
      localStorage.setItem(STORAGE_KEYS.SESSION_TRACKING, JSON.stringify(sessionTracking));
    }
  } catch (error) {
    console.error('Error marking session tracking:', error);
  }
};

// Mock exam result functions
export const saveMockExamResult = async (result: Omit<MockExamResult, 'id'>): Promise<void> => {
  try {
    // Save to Firebase
    const firebaseId = await firebaseService.saveMockExamResult(result);

    // Also save to localStorage as backup
    const localResults = getStoredData<MockExamResult[]>('mock_exam_results', []);
    localResults.push({
      ...result,
      id: firebaseId || Date.now().toString(),
    });
    localStorage.setItem('mock_exam_results', JSON.stringify(localResults));
  } catch (error) {
    console.error('Error saving mock exam result:', error);
    // Fallback to localStorage only if Firebase fails
    try {
      const localResults = getStoredData<MockExamResult[]>('mock_exam_results', []);
      localResults.push({
        ...result,
        id: Date.now().toString(),
      });
      localStorage.setItem('mock_exam_results', JSON.stringify(localResults));
    } catch (localError) {
      console.error('Error with localStorage fallback:', localError);
    }
  }
};

export const getMockExamLeaderboard = async (): Promise<MockExamResult[]> => {
  try {
    // Try to get from Firebase first
    const firebaseResults = await firebaseService.getMockExamLeaderboard();
    if (firebaseResults.length > 0) {
      return firebaseResults;
    }

    // Fallback to localStorage
    const localResults = getStoredData<MockExamResult[]>('mock_exam_results', []);
    return localResults
      .sort((a, b) => {
        if (b.score !== a.score) return b.score - a.score;
        return a.duration - b.duration;
      })
      .slice(0, 10);
  } catch (error) {
    console.error('Error fetching leaderboard:', error);
    // Fallback to localStorage
    try {
      const localResults = getStoredData<MockExamResult[]>('mock_exam_results', []);
      return localResults
        .sort((a, b) => {
          if (b.score !== a.score) return b.score - a.score;
          return a.duration - b.duration;
        })
        .slice(0, 10);
    } catch (localError) {
      console.error('Error with localStorage fallback:', localError);
      return [];
    }
  }
};

// Track page view (only once per session per page)
export const trackPageView = async (page: string, category?: string) => {
  try {
    const identifier = `${page}_${category || 'no-category'}`;

    // Check if already tracked in this session
    if (hasTrackedInSession('page_view', identifier)) {
      return; // Skip tracking if already tracked in this session
    }

    // Save to Firebase
    await firebaseService.savePageView({ page, category });

    // Also save to localStorage as backup
    const pageViews = getStoredData<PageView[]>(STORAGE_KEYS.PAGE_VIEWS, []);
    const newPageView: PageView = {
      page,
      timestamp: Date.now(),
      category,
    };
    pageViews.push(newPageView);
    localStorage.setItem(STORAGE_KEYS.PAGE_VIEWS, JSON.stringify(pageViews));

    // Mark as tracked in this session
    markTrackedInSession('page_view', identifier);
  } catch (error) {
    console.error('Error tracking page view:', error);
    // Fallback to localStorage only if Firebase fails
    try {
      const identifier = `${page}_${category || 'no-category'}`;
      const pageViews = getStoredData<PageView[]>(STORAGE_KEYS.PAGE_VIEWS, []);
      const newPageView: PageView = {
        page,
        timestamp: Date.now(),
        category,
      };
      pageViews.push(newPageView);
      localStorage.setItem(STORAGE_KEYS.PAGE_VIEWS, JSON.stringify(pageViews));
      markTrackedInSession('page_view', identifier);
    } catch (localError) {
      console.error('Error with localStorage fallback:', localError);
    }
  }
};

// Track test session
export const trackTestSession = async (type: 'mock' | 'category', category?: string, completed: boolean = false, score?: number) => {
  try {
    // Only prevent duplicate tracking for the same action within a short time window (5 seconds)
    const recentSessions = getStoredData<TestSession[]>(STORAGE_KEYS.TEST_SESSIONS, []);
    const fiveSecondsAgo = Date.now() - 5000;
    const recentDuplicate = recentSessions.find(session =>
      session.type === type &&
      session.category === category &&
      session.completed === completed &&
      session.timestamp > fiveSecondsAgo
    );

    if (recentDuplicate) {
      return; // Skip if same action was tracked within last 5 seconds
    }

    // Save to Firebase
    await firebaseService.saveTestSession({ type, category, completed, score });

    // Also save to localStorage as backup
    const testSessions = getStoredData<TestSession[]>(STORAGE_KEYS.TEST_SESSIONS, []);
    const newTestSession: TestSession = {
      type,
      category,
      timestamp: Date.now(),
      completed,
      score,
    };
    testSessions.push(newTestSession);
    localStorage.setItem(STORAGE_KEYS.TEST_SESSIONS, JSON.stringify(testSessions));
  } catch (error) {
    console.error('Error tracking test session:', error);
    // Fallback to localStorage only if Firebase fails
    try {
      const testSessions = getStoredData<TestSession[]>(STORAGE_KEYS.TEST_SESSIONS, []);
      const newTestSession: TestSession = {
        type,
        category,
        timestamp: Date.now(),
        completed,
        score,
      };
      testSessions.push(newTestSession);
      localStorage.setItem(STORAGE_KEYS.TEST_SESSIONS, JSON.stringify(testSessions));
    } catch (localError) {
      console.error('Error with localStorage fallback:', localError);
    }
  }
};

// Track study session (only once per session per category)
export const trackStudySession = async (category: string, questionsViewed: number) => {
  try {
    const identifier = `${category}_${questionsViewed}`;

    // Check if already tracked in this session
    if (hasTrackedInSession('study_session', identifier)) {
      return; // Skip tracking if already tracked in this session
    }

    // Save to Firebase
    await firebaseService.saveStudySession({ category, questionsViewed });

    // Also save to localStorage as backup
    const studySessions = getStoredData<StudySession[]>(STORAGE_KEYS.STUDY_SESSIONS, []);
    const newStudySession: StudySession = {
      category,
      timestamp: Date.now(),
      questionsViewed,
    };
    studySessions.push(newStudySession);
    localStorage.setItem(STORAGE_KEYS.STUDY_SESSIONS, JSON.stringify(studySessions));

    // Mark as tracked in this session
    markTrackedInSession('study_session', identifier);
  } catch (error) {
    console.error('Error tracking study session:', error);
    // Fallback to localStorage only if Firebase fails
    try {
      const identifier = `${category}_${questionsViewed}`;
      const studySessions = getStoredData<StudySession[]>(STORAGE_KEYS.STUDY_SESSIONS, []);
      const newStudySession: StudySession = {
        category,
        timestamp: Date.now(),
        questionsViewed,
      };
      studySessions.push(newStudySession);
      localStorage.setItem(STORAGE_KEYS.STUDY_SESSIONS, JSON.stringify(studySessions));
      markTrackedInSession('study_session', identifier);
    } catch (localError) {
      console.error('Error with localStorage fallback:', localError);
    }
  }
};

// Get stored data with fallback
const getStoredData = <T>(key: string, fallback: T): T => {
  try {
    const stored = localStorage.getItem(key);
    return stored ? JSON.parse(stored) : fallback;
  } catch (error) {
    console.error(`Error getting stored data for ${key}:`, error);
    return fallback;
  }
};

// Calculate statistics from stored data
export const calculateStatistics = async (): Promise<Statistics> => {
  try {
    // Try to get data from Firebase first, fallback to localStorage
    let pageViews: PageView[] = [];
    let testSessions: TestSession[] = [];
    let studySessions: StudySession[] = [];

    try {
      pageViews = await firebaseService.getPageViews();
      testSessions = await firebaseService.getTestSessions();
      studySessions = await firebaseService.getStudySessions();
    } catch (firebaseError) {
      console.warn('Firebase data fetch failed, using localStorage:', firebaseError);
      pageViews = getStoredData<PageView[]>(STORAGE_KEYS.PAGE_VIEWS, []);
      testSessions = getStoredData<TestSession[]>(STORAGE_KEYS.TEST_SESSIONS, []);
      studySessions = getStoredData<StudySession[]>(STORAGE_KEYS.STUDY_SESSIONS, []);
    }

    // Calculate page view stats
    const pageViewCounts: { [key: string]: number } = {};
    pageViews.forEach(pv => {
      pageViewCounts[pv.page] = (pageViewCounts[pv.page] || 0) + 1;
    });

    const sortedPageViews = Object.entries(pageViewCounts)
      .map(([page, views]) => ({ page, views }))
      .sort((a, b) => b.views - a.views);

    // Calculate category stats and mock exam stats
    const categoryStats: { [key: string]: { testTakers: number; studyViews: number } } = {};
    let mockExamTestTakers = 0;

    // Count test takers by category and mock exams (now only tracking completed tests)
    testSessions.forEach(ts => {
      if (ts.type === 'mock') {
        mockExamTestTakers++;
      } else if (ts.category) {
        if (!categoryStats[ts.category]) {
          categoryStats[ts.category] = { testTakers: 0, studyViews: 0 };
        }
        categoryStats[ts.category].testTakers++;
      }
    });

    // Count study views by category
    studySessions.forEach(ss => {
      if (!categoryStats[ss.category]) {
        categoryStats[ss.category] = { testTakers: 0, studyViews: 0 };
      }
      categoryStats[ss.category].studyViews++;
    });

    // Find most popular category
    const categoryTotals = Object.entries(categoryStats).map(([name, stats]) => ({
      name,
      total: stats.testTakers + stats.studyViews,
    }));
    const mostPopularCategory = categoryTotals.length > 0
      ? categoryTotals.sort((a, b) => b.total - a.total)[0].name
      : 'ไม่มีข้อมูล';

    // Get mock exam leaderboard
    const mockExamLeaderboard = await getMockExamLeaderboard();

    return {
      totalPageViews: pageViews.length,
      totalTestTakers: testSessions.length,
      totalStudyViews: studySessions.length,
      mockExamTestTakers: mockExamTestTakers,
      mostPopularPage: sortedPageViews.length > 0 ? sortedPageViews[0].page : 'ไม่มีข้อมูล',
      mostPopularCategory,
      categoryStats: Object.entries(categoryStats).map(([name, stats]) => ({
        name,
        testTakers: stats.testTakers,
        studyViews: stats.studyViews,
      })).sort((a, b) => (b.testTakers + b.studyViews) - (a.testTakers + a.studyViews)),
      pageViews: sortedPageViews,
      mockExamLeaderboard,
    };
  } catch (error) {
    console.error('Error calculating statistics:', error);
    // Return empty statistics on error
    return {
      totalPageViews: 0,
      totalTestTakers: 0,
      totalStudyViews: 0,
      mockExamTestTakers: 0,
      mostPopularPage: 'ไม่มีข้อมูล',
      mostPopularCategory: 'ไม่มีข้อมูล',
      categoryStats: [],
      pageViews: [],
      mockExamLeaderboard: [],
    };
  }
};

// Clear all statistics (for testing/reset purposes)
export const clearStatistics = () => {
  try {
    localStorage.removeItem(STORAGE_KEYS.PAGE_VIEWS);
    localStorage.removeItem(STORAGE_KEYS.TEST_SESSIONS);
    localStorage.removeItem(STORAGE_KEYS.STUDY_SESSIONS);
    localStorage.removeItem(STORAGE_KEYS.SESSION_TRACKING);
  } catch (error) {
    console.error('Error clearing statistics:', error);
  }
};
