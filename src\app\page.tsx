'use client';

import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { categories, mockExamConfig } from './data';
import MockExamModal from './components/MockExamModal';
import CategoryModal from './components/CategoryModal';
import { IBM_Plex_Sans_Thai } from "next/font/google";
import { useStatistics } from './hooks/useStatistics';
import { formatNumber } from './utils/numberFormatter';

const plexThai = IBM_Plex_Sans_Thai({
  subsets: ["latin"],
  variable: "--font-plex-thai",
  weight: "500",
});

export default function Home() {
  const router = useRouter();
  const [showMockExamModal, setShowMockExamModal] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [showCategories, setShowCategories] = useState(false);
  const [showStudyCategories, setShowStudyCategories] = useState(false);
  const [showStats, setShowStats] = useState(false);
  const [hasTrackedPageView, setHasTrackedPageView] = useState(false);

  // Real statistics data
  const { statistics, isLoading, trackPage } = useStatistics();

  // Track page view on component mount (only once per session)
  useEffect(() => {
    const trackPageView = async () => {
      if (!hasTrackedPageView) {
        await trackPage('หน้าแรก');
        setHasTrackedPageView(true);
      }
    };
    trackPageView();
  }, [hasTrackedPageView, trackPage]);

  // Reset statistics function (commented out since button is disabled)
  // const handleResetStats = () => {
  //   if (confirm('คุณต้องการรีเซ็ตสถิติทั้งหมดหรือไม่? การกระทำนี้ไม่สามารถย้อนกลับได้')) {
  //     clearStatistics();
  //     refreshStatistics();
  //   }
  // };

  // Refs for auto-scrolling
  const categoriesRef = useRef<HTMLDivElement>(null);
  const studyCategoriesRef = useRef<HTMLDivElement>(null);

  // Auto-scroll function
  const scrollToSection = (ref: React.RefObject<HTMLDivElement | null>) => {
    setTimeout(() => {
      ref.current?.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }, 100); // Small delay to ensure the section is rendered
  };

  return (
    <div className={`${plexThai.className} max-w-6xl mx-auto`}>
      {/* Hero Section */}
      <div className="text-center mb-8 md:mb-12 animate-fade-in">
        <h2 className="text-2xl md:text-4xl font-bold text-gray-800 mb-4">
          ยินดีต้อนรับสู่การทดสอบทำข้อสอบใบขับขี่
        </h2>
        <p className="text-base md:text-lg text-gray-600 mb-6 md:mb-8 px-4">
          เลือกรูปแบบการทดสอบที่คุณต้องการ
        </p>
      </div>

      {/* Statistics Section */}
      <div className="bg-white rounded-lg shadow-lg p-6 md:p-8 mb-8 md:mb-12 animate-fade-in">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl md:text-2xl font-bold text-gray-800">
            📊 สถิติการใช้งาน
          </h3>
          <div className="flex gap-2">
            <button
              onClick={() => router.push('/admin')}
              className="bg-green-100 hover:bg-green-200 text-green-700 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              title="ดูสถิติแบบละเอียด"
            >
              📊 แดชบอร์ด
            </button>
            {/* <button
              onClick={handleResetStats}
              className="bg-red-100 hover:bg-red-200 text-red-700 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              title="รีเซ็ตสถิติทั้งหมด"
            >
              🗑️ รีเซ็ต
            </button> */}
            <button
              onClick={() => setShowStats(!showStats)}
              className="bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-2 rounded-md text-sm font-medium transition-colors"
            >
              {showStats ? '🔼 ซ่อนรายละเอียด' : '🔽 ดูรายละเอียด'}
            </button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-6">
          <div className="bg-blue-50 rounded-lg p-4 text-center">
            <div className="text-2xl md:text-3xl font-bold text-blue-600">
              {isLoading ? '...' : formatNumber(statistics.totalPageViews)}
            </div>
            <div className="text-sm text-gray-600">ยอดวิวทั้งหมด</div>
          </div>
          <div className="bg-green-50 rounded-lg p-4 text-center">
            <div className="text-2xl md:text-3xl font-bold text-green-600">
              {isLoading ? '...' : formatNumber(statistics.totalTestTakers)}
            </div>
            <div className="text-sm text-gray-600">ผู้ทดสอบทั้งหมด</div>
          </div>
          <div className="bg-red-50 rounded-lg p-4 text-center">
            <div className="text-2xl md:text-3xl font-bold text-red-600">
              {isLoading ? '...' : formatNumber(statistics.mockExamTestTakers)}
            </div>
            <div className="text-sm text-gray-600">ข้อสอบเสมือนจริง</div>
          </div>
          <div className="bg-purple-50 rounded-lg p-4 text-center">
            <div className="text-2xl md:text-3xl font-bold text-purple-600">
              {isLoading ? '...' : formatNumber(statistics.totalStudyViews)}
            </div>
            <div className="text-sm text-gray-600">ยอดศึกษาทั้งหมด</div>
          </div>
          <div className="bg-orange-50 rounded-lg p-4 text-center">
            <div className="text-lg md:text-xl font-bold text-orange-600">
              {isLoading ? '...' : statistics.mostPopularCategory}
            </div>
            <div className="text-sm text-gray-600">หมวดหมู่ยอดนิยม</div>
          </div>
        </div>

        {/* Detailed Stats */}
        {showStats && (
          <div className="space-y-6 animate-fade-in">
            {/* Page Views */}
            <div>
              <h4 className="text-lg font-semibold text-gray-800 mb-4">📈 ยอดวิวแต่ละหน้า</h4>
              {statistics.pageViews.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {statistics.pageViews.map((page, index) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-4">
                      <div className="flex justify-between items-center">
                        <span className="font-medium text-gray-800">{page.page}</span>
                        <span className="text-blue-600 font-bold">{formatNumber(page.views)}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-500"
                          style={{ width: `${statistics.pageViews[0] ? (page.views / statistics.pageViews[0].views) * 100 : 0}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="bg-gray-50 rounded-lg p-8 text-center text-gray-500">
                  ยังไม่มีข้อมูลการเข้าชมหน้าต่างๆ
                </div>
              )}
            </div>

            {/* Category Stats */}
            <div>
              <h4 className="text-lg font-semibold text-gray-800 mb-4">📚 สถิติตามหมวดหมู่</h4>
              {statistics.categoryStats.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="text-left p-3 font-semibold">หมวดหมู่</th>
                        <th className="text-center p-3 font-semibold">ผู้ทดสอบ</th>
                        <th className="text-center p-3 font-semibold">ยอดศึกษา</th>
                        <th className="text-center p-3 font-semibold">รวม</th>
                      </tr>
                    </thead>
                    <tbody>
                      {statistics.categoryStats.map((category, index) => (
                        <tr key={index} className="border-b border-gray-200 hover:bg-gray-50">
                          <td className="p-3 font-medium">{category.name}</td>
                          <td className="p-3 text-center text-blue-600 font-semibold">
                            {formatNumber(category.testTakers)}
                          </td>
                          <td className="p-3 text-center text-green-600 font-semibold">
                            {formatNumber(category.studyViews)}
                          </td>
                          <td className="p-3 text-center text-purple-600 font-bold">
                            {formatNumber(category.testTakers + category.studyViews)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="bg-gray-50 rounded-lg p-8 text-center text-gray-500">
                  ยังไม่มีข้อมูลการใช้งานตามหมวดหมู่
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Mock Exam Leaderboard */}
      <div className="mb-8 md:mb-12">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
            🏆 อันดับคะแนนข้อสอบเสมือนจริง (Top 10)
          </h3>
          <div className="overflow-x-auto">
            {!isLoading && statistics.mockExamLeaderboard.length > 0 ? (
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b-2 border-gray-200">
                    <th className="p-3 text-left">อันดับ</th>
                    <th className="p-3 text-left">ชื่อ</th>
                    <th className="p-3 text-center">คะแนน</th>
                    <th className="p-3 text-center">เวลา</th>
                    <th className="p-3 text-center">วันที่ทำ</th>
                  </tr>
                </thead>
                <tbody>
                  {statistics.mockExamLeaderboard.map((result, index) => (
                    <tr key={result.id || index} className="border-b border-gray-200 hover:bg-gray-50">
                      <td className="p-3">
                        <div className="flex items-center">
                          {index === 0 && <span className="text-yellow-500 mr-1">🥇</span>}
                          {index === 1 && <span className="text-gray-400 mr-1">🥈</span>}
                          {index === 2 && <span className="text-orange-600 mr-1">🥉</span>}
                          <span className="font-semibold">{index + 1}</span>
                        </div>
                      </td>
                      <td className="p-3 font-medium">{result.userName}</td>
                      <td className="p-3 text-center">
                        <span className={`font-bold ${result.score >= 80 ? 'text-green-600' : result.score >= 60 ? 'text-yellow-600' : 'text-red-600'}`}>
                          {result.score}%
                        </span>
                      </td>
                      <td className="p-3 text-center text-gray-600">
                        {Math.floor(result.duration / 60)}:{(result.duration % 60).toString().padStart(2, '0')}
                      </td>
                      <td className="p-3 text-center text-gray-500 text-xs">
                        {new Date(result.timestamp).toLocaleDateString('th-TH')}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            ) : (
              <div className="text-center py-8 text-gray-500">
                ยังไม่มีข้อมูลการทำข้อสอบเสมือนจริง
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Options */}
      <div className="grid md:grid-cols-3 gap-6 md:gap-8 mb-8 md:mb-12">
        {/* Study Card */}
        <button
          onClick={() => {
            const newShowStudyCategories = !showStudyCategories;
            setShowStudyCategories(newShowStudyCategories);
            if (showCategories) setShowCategories(false);

            // Auto-scroll to study categories section when showing
            if (newShowStudyCategories) {
              scrollToSection(studyCategoriesRef);
            }
          }}
          className="bg-white rounded-lg shadow-lg p-6 md:p-8 hover:shadow-xl transition-all duration-300 hover:scale-105 animate-fade-in w-full text-left"
        >
          <div className="text-center">
            <div className="text-4xl md:text-6xl mb-4">📖</div>
            <h3 className="text-xl md:text-2xl font-bold text-gray-800 mb-4">
              ข้อสอบพร้อมคำตอบ
            </h3>
            <div className="text-sm md:text-base text-gray-600 mb-6">
              <p>ดูข้อสอบพร้อมคำตอบ</p>
              <p className="hidden md:block">ศึกษาก่อนทำข้อสอบจริง</p>
            </div>
            <div className="text-xs md:text-sm text-gray-500">
              {showStudyCategories ? '🔼 คลิกเพื่อซ่อนหมวดหมู่' : '🔽 คลิกเพื่อเลือกหมวดหมู่'}
            </div>
          </div>
        </button>

        {/* Category Exam Card */}
        <button
          onClick={() => {
            const newShowCategories = !showCategories;
            setShowCategories(newShowCategories);
            if (showStudyCategories) setShowStudyCategories(false);

            // Auto-scroll to categories section when showing
            if (newShowCategories) {
              scrollToSection(categoriesRef);
            }
          }}
          className="bg-white rounded-lg shadow-lg p-6 md:p-8 hover:shadow-xl transition-all duration-300 hover:scale-105 animate-fade-in w-full text-left"
        >
          <div className="text-center">
            <div className="text-4xl md:text-6xl mb-4">📚</div>
            <h3 className="text-xl md:text-2xl font-bold text-gray-800 mb-4">
              ข้อสอบตามหมวดหมู่
            </h3>
            <div className="text-sm md:text-base text-gray-600 mb-6">
              <p>เลือกหมวดหมู่ที่ต้องการทดสอบ</p>
              <p className="hidden md:block">จำนวนข้อสอบและเวลาขึ้นอยู่กับหมวดหมู่</p>
            </div>
            <div className="text-xs md:text-sm text-gray-500">
              {showCategories ? '🔼 คลิกเพื่อซ่อนหมวดหมู่' : '🔽 คลิกเพื่อเลือกหมวดหมู่'}
            </div>
          </div>
        </button>

        {/* Mock Exam Card */}
        <div className="bg-white rounded-lg shadow-lg p-6 md:p-8 hover:shadow-xl transition-all duration-300 hover:scale-105 animate-fade-in">
          <div className="text-center">
            <div className="text-4xl md:text-6xl mb-4">📝</div>
            <h3 className="text-xl md:text-2xl font-bold text-gray-800 mb-4">
              ข้อสอบเสมือนจริง
            </h3>
            <div className="text-left text-sm md:text-base text-gray-600 mb-6 space-y-2">
              <p>• จำนวน {mockExamConfig.totalQuestions} ข้อ</p>
              <p>• ผ่านเกณฑ์ 90% หรือ {mockExamConfig.passingScore} ข้อ</p>
              <p>• จับเวลา {mockExamConfig.timeLimit} นาที</p>
            </div>
            <button
              onClick={() => setShowMockExamModal(true)}
              className="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 md:px-8 py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-blue-800 transition-all duration-300 w-full shadow-md hover:shadow-lg"
            >
              เริ่มทดสอบ
            </button>
          </div>
        </div>

      </div>

      {/* Categories Grid for Exam */}
      {showCategories && (
        <div ref={categoriesRef} className="bg-white rounded-lg shadow-lg p-6 md:p-8 animate-fade-in">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl md:text-2xl font-bold text-gray-800">
              หมวดหมู่ข้อสอบ - ทดสอบ
            </h3>
            <button
              onClick={() => setShowCategories(false)}
              className="text-gray-500 hover:text-gray-700 transition-colors"
              title="ซ่อนหมวดหมู่"
            >
              ✕
            </button>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 md:gap-4">
            {categories.map((category, index) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className="bg-gray-50 hover:bg-blue-50 border border-gray-200 hover:border-blue-300 rounded-lg p-3 md:p-4 text-left transition-all duration-300 hover:scale-105 hover:shadow-md animate-fade-in"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="text-xl md:text-2xl mb-2">{category.icon}</div>
                <h4 className="font-semibold text-gray-800 mb-1 text-xs md:text-sm leading-tight">
                  {category.name}
                </h4>
                <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                  {category.description}
                </p>
                <div className="flex justify-between items-center mb-2">
                  <p className="text-xs text-blue-600 font-medium">
                    {category.count} ข้อ
                  </p>
                  <div className="text-xs text-gray-400">
                    {Math.ceil(category.count * 0.9)} ข้อผ่าน
                  </div>
                </div>
                {/* <div className="text-center">
                  <span className="inline-block px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full font-medium">
                    📝 ทดสอบ
                  </span>
                </div> */}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Categories Grid for Study */}
      {showStudyCategories && (
        <div ref={studyCategoriesRef} className="bg-white rounded-lg shadow-lg p-6 md:p-8 animate-fade-in">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl md:text-2xl font-bold text-gray-800">
              หมวดหมู่ข้อสอบ - ศึกษา
            </h3>
            <button
              onClick={() => setShowStudyCategories(false)}
              className="text-gray-500 hover:text-gray-700 transition-colors"
              title="ซ่อนหมวดหมู่"
            >
              ✕
            </button>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 md:gap-4">
            {categories.map((category, index) => (
              <button
                key={category.id}
                onClick={() => router.push(`/study?category=${category.id}`)}
                className="bg-gray-50 hover:bg-green-50 border border-gray-200 hover:border-green-300 rounded-lg p-3 md:p-4 text-left transition-all duration-300 hover:scale-105 hover:shadow-md animate-fade-in"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="text-xl md:text-2xl mb-2">{category.icon}</div>
                <h4 className="font-semibold text-gray-800 mb-1 text-xs md:text-sm leading-tight">
                  {category.name}
                </h4>
                <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                  {category.description}
                </p>
                <div className="flex justify-between items-center mb-2">
                  <p className="text-xs text-green-600 font-medium">
                    {category.count} ข้อ
                  </p>
                  <div className="text-xs text-gray-400">
                    ศึกษาพร้อมคำตอบ
                  </div>
                </div>
                {/* <div className="text-center">
                  <span className="inline-block px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full font-medium">
                    📖 ศึกษา
                  </span>
                </div> */}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Modals */}
      {showMockExamModal && (
        <MockExamModal onClose={() => setShowMockExamModal(false)} />
      )}

      {selectedCategory && (
        <CategoryModal
          categoryId={selectedCategory}
          onClose={() => setSelectedCategory(null)}
        />
      )}
    </div>
  );
}
