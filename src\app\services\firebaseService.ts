import {
  collection,
  addDoc,
  getDocs,
  query,
  orderBy,
  limit,
  serverTimestamp,
  Timestamp,
  doc,
  updateDoc,
  increment,
  getDoc,
  setDoc
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { PageView, TestSession, StudySession, MockExamResult } from '../utils/statistics';

// Collection names
const COLLECTIONS = {
  PAGE_VIEWS: 'pageViews',
  TEST_SESSIONS: 'testSessions',
  STUDY_SESSIONS: 'studySessions',
  MOCK_EXAM_RESULTS: 'mockExamResults',
  STATISTICS_SUMMARY: 'statisticsSummary'
};

// Firebase service class
export class FirebaseService {
  
  // Save page view to Firebase
  async savePageView(pageView: Omit<PageView, 'timestamp'>): Promise<void> {
    try {
      await addDoc(collection(db, COLLECTIONS.PAGE_VIEWS), {
        ...pageView,
        timestamp: serverTimestamp()
      });
      
      // Update summary statistics
      await this.updateSummaryStats('totalPageViews', 1);
    } catch (error) {
      console.error('Error saving page view:', error);
      throw error;
    }
  }

  // Save test session to Firebase
  async saveTestSession(testSession: Omit<TestSession, 'timestamp'>): Promise<void> {
    try {
      await addDoc(collection(db, COLLECTIONS.TEST_SESSIONS), {
        ...testSession,
        timestamp: serverTimestamp()
      });
      
      // Update summary statistics
      await this.updateSummaryStats('totalTestTakers', 1);
      if (testSession.type === 'mock') {
        await this.updateSummaryStats('mockExamTestTakers', 1);
      }
    } catch (error) {
      console.error('Error saving test session:', error);
      throw error;
    }
  }

  // Save study session to Firebase
  async saveStudySession(studySession: Omit<StudySession, 'timestamp'>): Promise<void> {
    try {
      await addDoc(collection(db, COLLECTIONS.STUDY_SESSIONS), {
        ...studySession,
        timestamp: serverTimestamp()
      });
      
      // Update summary statistics
      await this.updateSummaryStats('totalStudyViews', 1);
    } catch (error) {
      console.error('Error saving study session:', error);
      throw error;
    }
  }

  // Save mock exam result to Firebase
  async saveMockExamResult(result: Omit<MockExamResult, 'id' | 'timestamp'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, COLLECTIONS.MOCK_EXAM_RESULTS), {
        ...result,
        timestamp: serverTimestamp()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error saving mock exam result:', error);
      throw error;
    }
  }

  // Get page views from Firebase
  async getPageViews(): Promise<PageView[]> {
    try {
      const q = query(collection(db, COLLECTIONS.PAGE_VIEWS), orderBy('timestamp', 'desc'));
      const querySnapshot = await getDocs(q);
      
      return querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          page: data.page,
          category: data.category,
          timestamp: data.timestamp instanceof Timestamp ? data.timestamp.toMillis() : Date.now()
        };
      });
    } catch (error) {
      console.error('Error getting page views:', error);
      return [];
    }
  }

  // Get test sessions from Firebase
  async getTestSessions(): Promise<TestSession[]> {
    try {
      const q = query(collection(db, COLLECTIONS.TEST_SESSIONS), orderBy('timestamp', 'desc'));
      const querySnapshot = await getDocs(q);
      
      return querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          type: data.type,
          category: data.category,
          completed: data.completed,
          score: data.score,
          timestamp: data.timestamp instanceof Timestamp ? data.timestamp.toMillis() : Date.now()
        };
      });
    } catch (error) {
      console.error('Error getting test sessions:', error);
      return [];
    }
  }

  // Get study sessions from Firebase
  async getStudySessions(): Promise<StudySession[]> {
    try {
      const q = query(collection(db, COLLECTIONS.STUDY_SESSIONS), orderBy('timestamp', 'desc'));
      const querySnapshot = await getDocs(q);
      
      return querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          category: data.category,
          questionsViewed: data.questionsViewed,
          timestamp: data.timestamp instanceof Timestamp ? data.timestamp.toMillis() : Date.now()
        };
      });
    } catch (error) {
      console.error('Error getting study sessions:', error);
      return [];
    }
  }

  // Get mock exam leaderboard from Firebase
  async getMockExamLeaderboard(): Promise<MockExamResult[]> {
    try {
      const q = query(
        collection(db, COLLECTIONS.MOCK_EXAM_RESULTS), 
        orderBy('score', 'desc'),
        orderBy('duration', 'asc'),
        limit(10)
      );
      const querySnapshot = await getDocs(q);
      
      return querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          userName: data.userName,
          userEmail: data.userEmail,
          score: data.score,
          duration: data.duration,
          timestamp: data.timestamp instanceof Timestamp ? data.timestamp.toMillis() : Date.now()
        };
      });
    } catch (error) {
      console.error('Error getting mock exam leaderboard:', error);
      return [];
    }
  }

  // Update summary statistics
  private async updateSummaryStats(field: string, incrementValue: number): Promise<void> {
    try {
      const summaryRef = doc(db, COLLECTIONS.STATISTICS_SUMMARY, 'main');
      await updateDoc(summaryRef, {
        [field]: increment(incrementValue),
        lastUpdated: serverTimestamp()
      });
    } catch (error) {
      // If document doesn't exist, create it
      if (error instanceof Error && error.message.includes('No document to update')) {
        const summaryRef = doc(db, COLLECTIONS.STATISTICS_SUMMARY, 'main');
        await setDoc(summaryRef, {
          [field]: incrementValue,
          lastUpdated: serverTimestamp()
        }, { merge: true });
      } else {
        console.error('Error updating summary stats:', error);
      }
    }
  }

  // Get summary statistics
  async getSummaryStats(): Promise<Record<string, unknown>> {
    try {
      const summaryRef = doc(db, COLLECTIONS.STATISTICS_SUMMARY, 'main');
      const docSnap = await getDoc(summaryRef);
      
      if (docSnap.exists()) {
        return docSnap.data();
      } else {
        return {};
      }
    } catch (error) {
      console.error('Error getting summary stats:', error);
      return {};
    }
  }
}

// Export singleton instance
export const firebaseService = new FirebaseService();
