import type { Metadata } from "next";
import { <PERSON><PERSON><PERSON>, IBM_Plex_Sans_Thai } from "next/font/google";
import "./globals.css";
import FirebaseInitializer from "./components/FirebaseInitializer";


const sarabun = Sarabun({
  subsets: ["latin", "thai"],
  variable: "--font-sarabun",
  weight: ["300", "400", "500", "600", "700"],
});

const plexThai = IBM_Plex_Sans_Thai({
  subsets: ["latin"],
  variable: "--font-plex-thai",
  weight: "500",
});

export const metadata: Metadata = {
  title: "ข้อสอบใบขับขี่ - ระบบทดสอบออนไลน์",
  description: "ทดสอบข้อสอบใบขับขี่ออนไลน์ พร้อมข้อสอบเสมือนจริงและข้อสอบตามหมวดหมู่",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="th">
      <body
        className={`${sarabun.className} antialiased bg-gradient-to-b from-blue-200 to-white min-h-screen`}
      >
        <FirebaseInitializer>
          <div className="min-h-screen flex flex-col ">
            <header className={`${plexThai.className} bg-blue-600 text-white shadow-lg`}>
              <div className="container mx-auto px-4 py-4">
                <h1 className="text-2xl font-bold text-center">
                  ข้อสอบใบขับขี่รถยนต์ – รถจักรยานยนต์
                </h1>
              </div>
            </header>
            <main className=" flex-1 container mx-auto px-4 py-8">
              {children}
            </main>
            <footer className={`${plexThai.className} bg-gray-800 text-white py-4`}>
              <div className="container mx-auto px-4 text-center">
                <p>&copy; 2025 ข้อสอบใบขับขี่รถยนต์ – รถจักรยานยนต์ By SiNgH-kA</p>
              </div>
            </footer>
          </div>
        </FirebaseInitializer>
      </body>
    </html>
  );
}
