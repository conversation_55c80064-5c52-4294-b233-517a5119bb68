import { useState, useEffect } from 'react';
import { Statistics, calculateStatistics, trackPageView, trackTestSession, trackStudySession } from '../utils/statistics';

export const useStatistics = () => {
  const [statistics, setStatistics] = useState<Statistics>({
    totalPageViews: 0,
    totalTestTakers: 0,
    totalStudyViews: 0,
    mockExamTestTakers: 0,
    mostPopularPage: 'ไม่มีข้อมูล',
    mostPopularCategory: 'ไม่มีข้อมูล',
    categoryStats: [],
    pageViews: [],
    mockExamLeaderboard: [],
  });
  const [isLoading, setIsLoading] = useState(true);

  // Load statistics on mount and when localStorage changes
  const loadStatistics = () => {
    try {
      const stats = calculateStatistics();
      setStatistics(stats);
    } catch (error) {
      console.error('Error loading statistics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadStatistics();

    // Listen for storage changes (when other tabs update statistics)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key?.startsWith('driving_theory_')) {
        loadStatistics();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  // Tracking functions that also update local state
  const trackPage = (page: string, category?: string) => {
    trackPageView(page, category);
    loadStatistics(); // Refresh statistics
  };

  const trackTest = (
    type: 'mock' | 'category',
    category?: string,
    completed: boolean = false,
    score?: number
  ) => {
    trackTestSession(type, category, completed, score);
    loadStatistics(); // Refresh statistics
  };

  const trackStudy = (category: string, questionsViewed: number) => {
    trackStudySession(category, questionsViewed);
    loadStatistics(); // Refresh statistics
  };

  return {
    statistics,
    isLoading,
    trackPage,
    trackTest,
    trackStudy,
    refreshStatistics: loadStatistics,
  };
};
