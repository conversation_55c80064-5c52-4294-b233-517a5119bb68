'use client';

import { useState, useEffect } from 'react';

interface TimerProps {
  initialMinutes: number;
  onTimeUp: () => void;
}

export default function Timer({ initialMinutes, onTimeUp }: TimerProps) {
  const [timeLeft, setTimeLeft] = useState(initialMinutes * 60); // Convert to seconds
  const [isWarning, setIsWarning] = useState(false);

  useEffect(() => {
    if (timeLeft <= 0) {
      onTimeUp();
      return;
    }

    // Set warning when 5 minutes or less remaining
    setIsWarning(timeLeft <= 300);

    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          onTimeUp();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [timeLeft, onTimeUp]);

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className={`text-center p-3 md:p-4 rounded-lg border-2 transition-all duration-300 ${
      isWarning
        ? 'border-red-500 bg-red-50 text-red-700 shadow-lg'
        : 'border-blue-500 bg-blue-50 text-blue-700 shadow-md'
    }`}>
      <div className="text-xs md:text-sm font-medium mb-1">เวลาที่เหลือ</div>
      <div className={`text-lg md:text-2xl font-bold ${isWarning ? 'animate-pulse-custom' : ''}`}>
        {formatTime(timeLeft)}
      </div>
      {isWarning && (
        <div className="text-xs mt-1 font-medium animate-pulse">
          ⚠️ เวลาใกล้หมด!
        </div>
      )}
    </div>
  );
}
