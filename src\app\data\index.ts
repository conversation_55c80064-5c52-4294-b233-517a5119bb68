// Import all quiz data
import { quizData as hazardperception1Data } from './hazardperception1';
import { quizData as hazardperception2Data } from './hazardperception2';
import { quizData as vehicleslawData } from './vehicleslaw';
import { quizData as trafficlawData } from './trafficlaw';
import { quizData as trafficData } from './traffic';
import { quizData as markingsData } from './markings';
import { quizData as mandatorysignData } from './mandatorysign';
import { quizData as warningData } from './warning';
import { quizData as awarenessData } from './awareness';
import { quizData as safedrivingData } from './safedriving';
import { quizData as maintenanceData } from './maintenance';
import { quizData as trafficlawimageData } from './trafficlawimage';

// Common types
export type Option = {
  text?: string;
  image?: string;
};

export type Question = {
  numbers: string;
  question: Option[];
  options: Option[];
  correctAnswer: number;
  order: ("text" | "image")[];
};

// Category information
export interface CategoryInfo {
  id: string;
  name: string;
  data: Question[];
  count: number;
  icon: string;
  description: string;
}

// Quiz categories with their data
export const categories: CategoryInfo[] = [
  {
    id: 'hazardperception1',
    name: 'การคาดการณ์อุบัติเหตุ ฉบับที่ 1',
    data: hazardperception1Data,
    count: hazardperception1Data.length,
    icon: '🚑',
    description: 'ทักษะการคาดการณ์และป้องกันอุบัติเหตุ'
  },
  {
    id: 'hazardperception2',
    name: 'การคาดการณ์อุบัติเหตุ ฉบับที่ 2',
    data: hazardperception2Data,
    count: hazardperception2Data.length,
    icon: '🚑',
    description: 'ทักษะการคาดการณ์และป้องกันอุบัติเหตุ ระดับสูง'
  },
  {
    id: 'vehicleslaw',
    name: 'กฎหมายว่าด้วยรถยนต์',
    data: vehicleslawData,
    count: vehicleslawData.length,
    icon: '🚗',
    description: 'กฎหมายและข้อบังคับเกี่ยวกับรถยนต์'
  },
  {
    id: 'trafficlaw',
    name: 'กฎหมายว่าด้วยการจราจรทางบก',
    data: trafficlawData,
    count: trafficlawData.length,
    icon: '📋',
    description: 'กฎหมายจราจรและข้อบังคับการขับขี่'
  },
  {
    id: 'traffic',
    name: 'สัญญาณ',
    data: trafficData,
    count: trafficData.length,
    icon: '🚦',
    description: 'สัญญาณไฟจราจรและสัญญาณต่างๆ'
  },
  {
    id: 'markings',
    name: 'เครื่องหมายบนพื้นทาง-ขอบทาง',
    data: markingsData,
    count: markingsData.length,
    icon: '🛣️',
    description: 'เครื่องหมายและเส้นบนถนน'
  },
  {
    id: 'mandatorysign',
    name: 'ป้ายบังคับ',
    data: mandatorysignData,
    count: mandatorysignData.length,
    icon: '🚫',
    description: 'ป้ายบังคับและข้อห้าม'
  },
  {
    id: 'warning',
    name: 'ป้ายเตือน ป้ายแนะนำ',
    data: warningData,
    count: warningData.length,
    icon: '⚠️',
    description: 'ป้ายเตือนและป้ายแนะนำ'
  },
  {
    id: 'maintenance',
    name: 'การบำรุงรักษารถ',
    data: maintenanceData,
    count: maintenanceData.length,
    icon: '🔧',
    description: 'การดูแลและบำรุงรักษารถยนต์'
  },
  {
    id: 'safedriving',
    name: 'การขับรถอย่างปลอดภัย',
    data: safedrivingData,
    count: safedrivingData.length,
    icon: '🛡️',
    description: 'หลักการขับรถอย่างปลอดภัย'
  },
  {
    id: 'awareness',
    name: 'จิตสำนึกและมารยาทในการขับรถ',
    data: awarenessData,
    count: awarenessData.length,
    icon: '🧠',
    description: 'จิตสำนึกและมารยาทของผู้ขับขี่'
  },
  {
    id: 'trafficlawimage',
    name: 'รูปภาพกฎหมายจราจร',
    data: trafficlawimageData,
    count: trafficlawimageData.length,
    icon: '📷',
    description: 'กฎหมายจราจรในรูปแบบภาพ'
  }
];

// Mock exam configuration (50 questions total)
export const mockExamConfig = {
  totalQuestions: 50,
  passingScore: 45, // 90%
  timeLimit: 60, // minutes
  categories: [
    { id: 'hazardperception1', count: 2 },
    { id: 'hazardperception2', count: 2 },
    { id: 'vehicleslaw', count: 9 },
    { id: 'trafficlaw', count: 6 },
    { id: 'traffic', count: 4 },
    { id: 'markings', count: 1 },
    { id: 'mandatorysign', count: 1 },
    { id: 'warning', count: 8 },
    { id: 'maintenance', count: 3 },
    { id: 'safedriving', count: 8 },
    { id: 'awareness', count: 1 },
    { id: 'trafficlawimage', count: 5 }
  ]
};

// Function to get category by id
export const getCategoryById = (id: string): CategoryInfo | undefined => {
  return categories.find(category => category.id === id);
};

// Function to generate ordered mock exam (1-50)
export const generateMockExam = (): Question[] => {
  const mockQuestions: Question[] = [];
  let questionNumber = 1;

  mockExamConfig.categories.forEach(({ id, count }) => {
    const category = getCategoryById(id);
    if (category && category.data.length >= count) {
      // Take first 'count' questions from category (in order)
      const categoryQuestions = category.data.slice(0, count);

      // Add question numbers to maintain order
      categoryQuestions.forEach(question => {
        mockQuestions.push({
          ...question,
          numbers: `ข้อ ${questionNumber}` // Override with sequential numbering
        });
        questionNumber++;
      });
    }
  });

  // Return questions in order (1-50)
  return mockQuestions;
};

// Function to get all questions from a specific category
export const getCategoryQuestions = (categoryId: string): Question[] => {
  const category = getCategoryById(categoryId);
  return category ? [...category.data] : [];
};

// Function to calculate passing score for category
export const getCategoryPassingScore = (categoryId: string): number => {
  const category = getCategoryById(categoryId);
  if (!category) return 0;
  return Math.ceil(category.count * 0.9); // 90% passing score
};

// Function to calculate time limit for category (1 minute per question)
export const getCategoryTimeLimit = (categoryId: string): number => {
  const category = getCategoryById(categoryId);
  return category ? category.count : 0; // minutes
};
