# 🚗 ระบบข้อสอบใบขับขี่รถยนต์ – รถจักรยานยนต์

ระบบทดสอบความรู้สำหรับการสอบใบขับขี่ พร้อมระบบศึกษาข้อสอบและสถิติการใช้งาน

## ✨ Features

- 📝 **ข้อสอบเสมือนจริง** - ข้อสอบแบบสุ่มจากทุกหมวดหมู่
- 📚 **ข้อสอบตามหมวดหมู่** - เลือกทำข้อสอบเฉพาะหมวดหมู่
- 🎓 **ศึกษาข้อสอบ** - ดูข้อสอบพร้อมเฉลย
- 📊 **สถิติการใช้งาน** - ติดตามการใช้งานแบบ Real-time
- ⏱️ **จับเวลาสอบ** - ระบบจับเวลาอัตโนมัติ
- 📱 **Responsive Design** - ใช้งานได้ทุกอุปกรณ์
- 🎨 **UI/UX ที่สวยงาม** - ออกแบบให้ใช้งานง่าย

## 🛠️ Technology Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS 4
- **Icons**: React Icons
- **Fonts**: Sarabun (Thai), IBM Plex Sans Thai
- **Storage**: localStorage (Client-side)
- **Build**: Static Export (No Server Required)

## 🚀 Quick Start

### Development
```bash
# Install dependencies
npm install

# Run development server
npm run dev

# Open http://localhost:3000
```

### Production Build
```bash
# Build for production
npm run build

# Files will be generated in 'out/' directory
```

## 📁 Project Structure

```
src/
├── app/
│   ├── components/          # Reusable components
│   ├── data/               # Question data and categories
│   ├── hooks/              # Custom React hooks
│   ├── utils/              # Utility functions
│   ├── exam/               # Exam page
│   ├── study/              # Study page
│   ├── results/            # Results page
│   └── globals.css         # Global styles
├── public/
│   └── trafficlawimage/    # Question images
└── ...
```

## 📊 Statistics System

ระบบติดตามสถิติการใช้งานแบบ Real-time:

- **Page Views** - จำนวนการเข้าชมแต่ละหน้า
- **Test Sessions** - สถิติการทำข้อสอบ
- **Study Sessions** - สถิติการศึกษา
- **Category Analytics** - การใช้งานแยกตามหมวดหมู่

## 🎯 Question Categories

1. **เครื่องหมายจราจร** - Traffic Signs
2. **กฎหมายจราจร** - Traffic Laws
3. **การขับรถอย่างปลอดภัย** - Safe Driving
4. **จิตสำนึกและมารยาทในการขับรถ** - Driving Ethics
5. **การบำรุงรักษารถ** - Vehicle Maintenance
6. **การขับรถในสภาวะต่างๆ** - Driving Conditions

## 🔧 Configuration

### Next.js Config
```typescript
// next.config.ts
const nextConfig: NextConfig = {
  output: 'export',        // Static export
  trailingSlash: true,     // Add trailing slash
  images: {
    unoptimized: true      // Disable image optimization
  }
};
```

### Font Configuration
```typescript
// Sarabun font for Thai text
const sarabun = Sarabun({
  subsets: ["latin", "thai"],
  weight: ["300", "400", "500", "600", "700"],
});
```

## 📱 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 🚀 Deployment

ดูรายละเอียดการ Deploy ใน [DEPLOYMENT.md](./DEPLOYMENT.md)

### Quick Deploy Options:
- **Netlify**: Drag & drop `out/` folder
- **Vercel**: `vercel --prod`
- **GitHub Pages**: Upload to gh-pages branch
- **Any Static Hosting**: Upload `out/` contents

## 🎨 Design Features

- **Modern UI** - Clean and professional design
- **Thai Typography** - Optimized for Thai language
- **Compact Numbers** - Statistics in k/M format (e.g., 2.330k)
- **Smooth Animations** - Engaging user experience
- **Mobile First** - Responsive across all devices

## 📈 Performance

- **First Load JS**: ~131 kB
- **Page Size**: 3-5 kB per page
- **Build Time**: ~2-3 seconds
- **Lighthouse Score**: 95+ (Performance)

## 🔄 Development Workflow

1. **Development**: `npm run dev`
2. **Testing**: Manual testing on different devices
3. **Build**: `npm run build`
4. **Deploy**: Upload `out/` directory

## 📝 License

This project is created for educational purposes.

## 👨‍💻 Created By

**Augment Agent** - AI-powered development assistant

---

**เวอร์ชัน**: 1.0.0
**วันที่สร้าง**: 19 มกราคม 2025
**ภาษา**: TypeScript, Thai
