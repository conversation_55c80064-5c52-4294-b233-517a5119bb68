'use client';

import { useEffect, useState } from 'react';
import { analytics } from '../config/firebase';

interface FirebaseInitializerProps {
  children: React.ReactNode;
}

export default function FirebaseInitializer({ children }: FirebaseInitializerProps) {
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializeFirebase = async () => {
      try {
        // Firebase is already initialized in the config file
        // Just check if analytics is available
        if (analytics) {
          console.log('Firebase Analytics initialized successfully');
        }
        
        setIsInitialized(true);
      } catch (err) {
        console.error('Firebase initialization error:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        setIsInitialized(true); // Still allow the app to work without Firebase
      }
    };

    initializeFirebase();
  }, []);

  if (!isInitialized) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">กำลังเชื่อมต่อกับระบบ...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center bg-yellow-50 border border-yellow-200 rounded-lg p-6 max-w-md">
          <div className="text-yellow-600 mb-2">
            <svg className="w-8 h-8 mx-auto" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-yellow-800 mb-2">การเชื่อมต่อมีปัญหา</h3>
          <p className="text-yellow-700 text-sm mb-4">
            ไม่สามารถเชื่อมต่อกับฐานข้อมูลได้ แต่ระบบยังคงทำงานได้ปกติ
          </p>
          <div className="text-xs text-yellow-600 bg-yellow-100 rounded p-2">
            {error}
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
