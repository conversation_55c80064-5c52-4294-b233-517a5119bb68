'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useStatistics } from '../hooks/useStatistics';
import { formatNumber } from '../utils/numberFormatter';
import { IBM_Plex_Sans_Thai } from "next/font/google";
import { FiHome, FiUsers, FiBookOpen, FiTrendingUp, FiDatabase, FiRefreshCw } from 'react-icons/fi';

const plexThai = IBM_Plex_Sans_Thai({
  subsets: ["latin"],
  variable: "--font-plex-thai",
  weight: "500",
});

export default function AdminPage() {
  const router = useRouter();
  const { statistics, isLoading, refreshStatistics } = useStatistics();
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await refreshStatistics();
    setTimeout(() => setIsRefreshing(false), 1000);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">กำลังโหลดสถิติ...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className={`${plexThai.className} text-3xl font-bold text-gray-800 mb-2`}>
            📊 สถิติระบบ - แดชบอร์ดผู้ดูแล
          </h1>
          <p className="text-gray-600">ข้อมูลการใช้งานระบบข้อสอบใบขับขี่แบบละเอียด</p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
          >
            <FiRefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            รีเฟรช
          </button>
          <button
            onClick={() => router.push('/')}
            className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            <FiHome className="w-4 h-4" />
            กลับหน้าหลัก
          </button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-blue-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">การเข้าชมทั้งหมด</p>
              <p className="text-2xl font-bold text-gray-900">{formatNumber(statistics.totalPageViews)}</p>
            </div>
            <FiTrendingUp className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">ผู้ทำข้อสอบ</p>
              <p className="text-2xl font-bold text-gray-900">{formatNumber(statistics.totalTestTakers)}</p>
            </div>
            <FiUsers className="w-8 h-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-purple-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">การศึกษา</p>
              <p className="text-2xl font-bold text-gray-900">{formatNumber(statistics.totalStudyViews)}</p>
            </div>
            <FiBookOpen className="w-8 h-8 text-purple-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-orange-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">ข้อสอบจำลอง</p>
              <p className="text-2xl font-bold text-gray-900">{formatNumber(statistics.mockExamTestTakers)}</p>
            </div>
            <FiDatabase className="w-8 h-8 text-orange-500" />
          </div>
        </div>
      </div>

      {/* Detailed Statistics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Popular Pages */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h3 className={`${plexThai.className} text-xl font-bold text-gray-800 mb-4`}>
            📄 หน้าที่ได้รับความนิยม
          </h3>
          <div className="space-y-3">
            {statistics.pageViews.slice(0, 5).map((page, index) => (
              <div key={page.page} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <span className="flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-600 rounded-full text-sm font-bold">
                    {index + 1}
                  </span>
                  <span className="font-medium">{page.page}</span>
                </div>
                <span className="text-blue-600 font-bold">{formatNumber(page.views)} ครั้ง</span>
              </div>
            ))}
          </div>
        </div>

        {/* Category Statistics */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h3 className={`${plexThai.className} text-xl font-bold text-gray-800 mb-4`}>
            📚 สถิติตามหมวดหมู่
          </h3>
          <div className="space-y-3">
            {statistics.categoryStats.slice(0, 5).map((category, index) => (
              <div key={category.name} className="p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium">{category.name}</span>
                  <span className="text-sm text-gray-500">#{index + 1}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-green-600">ทำข้อสอบ: {formatNumber(category.testTakers)}</span>
                  <span className="text-purple-600">ศึกษา: {formatNumber(category.studyViews)}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Mock Exam Leaderboard */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h3 className={`${plexThai.className} text-xl font-bold text-gray-800 mb-4`}>
          🏆 อันดับข้อสอบจำลอง (Top 10)
        </h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-semibold text-gray-700">อันดับ</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700">ชื่อ</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700">อีเมล</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700">คะแนน</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700">เวลา</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700">วันที่</th>
              </tr>
            </thead>
            <tbody>
              {statistics.mockExamLeaderboard.map((result, index) => (
                <tr key={result.id || index} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-3 px-4">
                    <span className={`inline-flex items-center justify-center w-8 h-8 rounded-full text-white font-bold ${
                      index === 0 ? 'bg-yellow-500' : 
                      index === 1 ? 'bg-gray-400' : 
                      index === 2 ? 'bg-orange-600' : 'bg-blue-500'
                    }`}>
                      {index + 1}
                    </span>
                  </td>
                  <td className="py-3 px-4 font-medium">{result.userName}</td>
                  <td className="py-3 px-4 text-gray-600">{result.userEmail}</td>
                  <td className="py-3 px-4">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      {result.score}/50
                    </span>
                  </td>
                  <td className="py-3 px-4 text-gray-600">
                    {Math.floor(result.duration / 60)}:{(result.duration % 60).toString().padStart(2, '0')}
                  </td>
                  <td className="py-3 px-4 text-gray-600">
                    {new Date(result.timestamp).toLocaleDateString('th-TH')}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          {statistics.mockExamLeaderboard.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              ยังไม่มีข้อมูลการทำข้อสอบจำลอง
            </div>
          )}
        </div>
      </div>

      {/* Summary Info */}
      <div className="mt-8 bg-blue-50 border border-blue-200 rounded-xl p-6">
        <h3 className={`${plexThai.className} text-lg font-bold text-blue-800 mb-2`}>
          📋 สรุปข้อมูล
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700">
          <div>
            <p><strong>หน้าที่ได้รับความนิยมมากที่สุด:</strong> {statistics.mostPopularPage}</p>
            <p><strong>หมวดหมู่ที่ได้รับความนิยมมากที่สุด:</strong> {statistics.mostPopularCategory}</p>
          </div>
          <div>
            <p><strong>ข้อมูลอัปเดตล่าสุด:</strong> {new Date().toLocaleString('th-TH')}</p>
            <p><strong>แหล่งข้อมูล:</strong> Firebase Database + localStorage (สำรอง)</p>
          </div>
        </div>
      </div>
    </div>
  );
}
