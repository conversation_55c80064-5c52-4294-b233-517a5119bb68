# 🚀 Deployment Guide - ระบบข้อสอบใบขับขี่

## 📦 Build และ Export ระบบ

### 1. Build สำหรับ Production
```bash
npm run build
```

### 2. Export เป็น Static Files
```bash
npm run export
```

### 3. Test Local Server
```bash
npm run serve
# หรือ
npx serve out
```

## 📁 ไฟล์ที่ได้จากการ Export

หลังจาก build เสร็จแล้ว ไฟล์ทั้งหมดจะอยู่ในโฟลเดอร์ `out/`:

```
out/
├── index.html          # หน้าแรก
├── exam/
│   └── index.html      # หน้าทำข้อสอบ
├── study/
│   └── index.html      # หน้าศึกษาข้อสอบ
├── results/
│   └── index.html      # หน้าผลการสอบ
├── _next/              # Static assets (CSS, JS)
├── trafficlawimage/    # รูปภาพข้อสอบ
└── *.svg, *.ico        # Icons และ assets อื่นๆ
```

## 🌐 วิธีการ Deploy

### 1. Web Server (Apache/Nginx)
- อัปโหลดไฟล์ทั้งหมดในโฟลเดอร์ `out/` ไปยัง web root directory
- ตั้งค่า URL rewriting สำหรับ SPA routing

### 2. Static Hosting Services
- **Netlify**: ลาก drop โฟลเดอร์ `out/` ลงใน Netlify
- **Vercel**: `vercel --prod`
- **GitHub Pages**: อัปโหลดไฟล์ใน `out/` ไปยัง gh-pages branch
- **Firebase Hosting**: `firebase deploy`

### 3. CDN Services
- **AWS S3 + CloudFront**
- **Google Cloud Storage**
- **Azure Static Web Apps**

## ⚙️ การตั้งค่า Web Server

### Apache (.htaccess)
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.html [L]
```

### Nginx
```nginx
location / {
    try_files $uri $uri/ /index.html;
}
```

## 📊 ข้อมูลสถิติ

ระบบใช้ localStorage สำหรับเก็บสถิติการใช้งาน:
- `driving_theory_page_views` - สถิติการเข้าชมหน้าต่างๆ
- `driving_theory_test_sessions` - สถิติการทำข้อสอบ
- `driving_theory_study_sessions` - สถิติการศึกษา
- `driving_theory_session_tracking` - ป้องกันการนับซ้ำ

## 🔧 การปรับแต่ง

### เปลี่ยน Base Path
หากต้องการติดตั้งในโฟลเดอร์ย่อย เช่น `/driving-theory/`:

1. แก้ไข `next.config.ts`:
```typescript
const nextConfig: NextConfig = {
  output: 'export',
  trailingSlash: true,
  basePath: '/driving-theory',
  images: {
    unoptimized: true
  }
};
```

2. Build ใหม่:
```bash
npm run build
```

## 📱 Browser Support

ระบบรองรับ:
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 🎯 Features

- ✅ Static Export (ไม่ต้องใช้ Server)
- ✅ Responsive Design
- ✅ PWA Ready
- ✅ SEO Optimized
- ✅ Fast Loading
- ✅ Offline Capable (localStorage)

## 📈 Performance

- **First Load JS**: ~131 kB
- **Page Size**: 3-5 kB per page
- **Build Time**: ~2-3 seconds
- **Static Files**: Ready for CDN

## 🚨 หมายเหตุ

1. ระบบใช้ localStorage ดังนั้นข้อมูลจะเก็บไว้ในเครื่องผู้ใช้
2. ไม่ต้องการ Database หรือ Backend
3. รองรับการใช้งาน Offline
4. สามารถ Deploy บน Static Hosting ได้ทุกแพลตฟอร์ม

## 🔄 การอัปเดต

เมื่อต้องการอัปเดตระบบ:
1. แก้ไขโค้ด
2. `npm run build`
3. อัปโหลดไฟล์ใหม่ใน `out/` ทับของเดิม

## 🛠️ Troubleshooting

### ปัญหา: `serve out` ไม่ทำงาน (404 Error)

**สาเหตุ**: โฟลเดอร์ `out/` ไม่มีหรือไม่ได้ถูกสร้าง

**วิธีแก้**:
```bash
# 1. Build ใหม่
npm run build

# 2. ตรวจสอบว่ามีโฟลเดอร์ out/
ls -la out/

# 3. Serve อีกครั้ง
npm run serve
```

### ปัญหา: หน้าเว็บแสดงแต่ไม่ทำงาน

**สาเหตุ**: JavaScript ไม่โหลดหรือ CORS issues

**วิธีแก้**:
- ใช้ `npx serve out` แทน `python -m http.server`
- ตรวจสอบ Console ใน Browser Developer Tools
- ตรวจสอบว่าไฟล์ `_next/` ถูก serve ได้

### ปัญหา: Routing ไม่ทำงาน (404 เมื่อ refresh)

**วิธีแก้**:
- ตั้งค่า URL rewriting ใน web server
- สำหรับ Apache: ใช้ `.htaccess`
- สำหรับ Nginx: ใช้ `try_files`

---

**สร้างโดย**: Augment Agent
**เวอร์ชัน**: 1.0.0
**วันที่**: 2025-01-19
